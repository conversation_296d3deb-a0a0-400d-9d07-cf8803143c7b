#!/usr/bin/env python3
"""
Test final des corrections :
1. G<PERSON><PERSON>ques matplotlib intégrés (plus de <PERSON>lotly)
2. Expand en overlay absolu (plus de poussée des widgets)
"""

import sys
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt
from nano_dashboard import NanoDashboard
from nano_style import get_test_button_style, get_test_info_style

class FinalFixesTestWindow(QMainWindow):
    """Fenêtre de test des corrections finales"""
    
    def __init__(self):
        super().__init__()
        self.dark_mode = True
        self.setup_ui()
        self.setup_dashboard()
        
    def setup_ui(self):
        """Configure l'interface"""
        self.setWindowTitle("🎯 CORRECTIONS FINALES - Matplotlib + Expand Overlay Absolu")
        self.setGeometry(50, 50, 1400, 1000)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Contrôles
        controls_layout = QHBoxLayout()
        
        # Bouton toggle dark/light mode
        self.toggle_mode_btn = QPushButton("🌙 Mode Clair")
        self.toggle_mode_btn.clicked.connect(self.toggle_dark_mode)
        controls_layout.addWidget(self.toggle_mode_btn)
        
        # Bouton test matplotlib
        test_matplotlib_btn = QPushButton("📊 Test Matplotlib")
        test_matplotlib_btn.clicked.connect(self.test_matplotlib)
        controls_layout.addWidget(test_matplotlib_btn)
        
        # Bouton test expand overlay
        test_expand_btn = QPushButton("⛶ Test Expand Overlay")
        test_expand_btn.clicked.connect(self.test_expand_overlay)
        controls_layout.addWidget(test_expand_btn)
        
        # Bouton vérifier Plotly supprimé
        check_plotly_btn = QPushButton("🚫 Vérifier Plotly Supprimé")
        check_plotly_btn.clicked.connect(self.check_plotly_removed)
        controls_layout.addWidget(check_plotly_btn)
        
        controls_layout.addStretch()
        
        # Style des boutons
        button_style = get_test_button_style()
        for btn in [self.toggle_mode_btn, test_matplotlib_btn, test_expand_btn, check_plotly_btn]:
            btn.setStyleSheet(button_style)
        
        main_layout.addLayout(controls_layout)
        
        # Dashboard
        self.dashboard = None
        
        # Info finale
        info_text = """
🎯 CORRECTIONS FINALES APPLIQUÉES:

✅ CORRECTION 1: PLOTLY COMPLÈTEMENT SUPPRIMÉ
   • Toutes les méthodes plot_*_plotly() supprimées
   • Plus d'import plotly dans nano_container.py
   • Graphiques matplotlib intégrés DANS le dashboard
   • Plus d'ouverture navigateur web

✅ CORRECTION 2: EXPAND EN OVERLAY ABSOLU
   • Widget original caché (pas déplacé)
   • Overlay créé au niveau fenêtre principale
   • Container expandé cloné dans l'overlay
   • Bouton fermeture intégré
   • Plus de poussée des autres widgets

🧪 TESTS DISPONIBLES:
   📊 Test Matplotlib: Vérifier graphiques intégrés
   ⛶ Test Expand: Vérifier overlay absolu
   🚫 Vérifier Plotly: Confirmer suppression complète

💡 RÉSULTAT ATTENDU:
   • Graphiques affichés DANS le dashboard
   • Expand au premier plan sans bouger les autres
   • Performance optimisée
   • Interface fluide
        """
        
        from PySide6.QtWidgets import QLabel
        info_label = QLabel(info_text)
        info_label.setStyleSheet(get_test_info_style())
        main_layout.addWidget(info_label)
        
    def setup_dashboard(self):
        """Configure le dashboard"""
        print("🎯 Création du dashboard avec corrections finales...")
        
        # Créer le dashboard
        self.dashboard = NanoDashboard(self.dark_mode)
        
        # Ajouter au layout
        self.centralWidget().layout().insertWidget(1, self.dashboard)
        
        print("✅ Dashboard créé avec corrections finales")
        
    def toggle_dark_mode(self):
        """Bascule entre mode sombre et clair"""
        self.dark_mode = not self.dark_mode
        
        if self.dark_mode:
            self.toggle_mode_btn.setText("🌙 Mode Clair")
        else:
            self.toggle_mode_btn.setText("☀️ Mode Sombre")
        
        if self.dashboard:
            self.dashboard.update_dark_mode(self.dark_mode)
        
        print(f"🎨 Mode basculé vers: {'Sombre' if self.dark_mode else 'Clair'}")
        
    def test_matplotlib(self):
        """Teste les graphiques matplotlib intégrés"""
        print("📊 TEST MATPLOTLIB INTÉGRÉ:")
        print("=" * 40)
        
        if self.dashboard:
            matplotlib_containers = []
            for widget in self.dashboard.contents.keys():
                if hasattr(widget, 'get_nano_container'):
                    nano = widget.get_nano_container()
                    if hasattr(nano, 'chart_generator') and nano.chart_generator:
                        matplotlib_containers.append(nano)
            
            print(f"✅ {len(matplotlib_containers)} containers matplotlib trouvés")
            
            if matplotlib_containers:
                # Tester un graphique
                container = matplotlib_containers[0]
                x_data = list(range(20))
                y_data = [50000 + i*300 for i in x_data]
                container.create_line_chart(x_data, y_data, "Test Matplotlib Intégré", "Time", "Price")
                print("✅ Graphique matplotlib créé et affiché DANS le dashboard")
                print("💡 Vérifiez que le graphique apparaît dans le container")
            else:
                print("❌ Aucun container matplotlib trouvé")
        else:
            print("❌ Dashboard non disponible")
            
    def test_expand_overlay(self):
        """Teste l'expand en overlay absolu"""
        print("⛶ TEST EXPAND OVERLAY ABSOLU:")
        print("=" * 40)
        
        if self.dashboard and self.dashboard.slot_order:
            # Expand le premier container
            first_widget = self.dashboard.slot_order[0]
            self.dashboard.expand_widget(first_widget)
            
            print("✅ Container expandé en overlay absolu")
            print("💡 Vérifiez que:")
            print("   • Le container apparaît au PREMIER PLAN")
            print("   • Les autres containers RESTENT EN PLACE")
            print("   • Background semi-transparent")
            print("   • Bouton 'Fermer' disponible")
            print("   • Aucun widget n'est poussé en dessous")
            
        else:
            print("❌ Dashboard non disponible")
            
    def check_plotly_removed(self):
        """Vérifie que Plotly est complètement supprimé"""
        print("🚫 VÉRIFICATION SUPPRESSION PLOTLY:")
        print("=" * 40)
        
        # Vérifier les imports
        try:
            from nano_container import MatplotlibChartContainer
            print("✅ MatplotlibChartContainer importé avec succès")
        except ImportError as e:
            print(f"❌ Erreur import MatplotlibChartContainer: {e}")
        
        # Vérifier qu'il n'y a plus de références Plotly
        plotly_methods_found = []
        if self.dashboard:
            for widget in self.dashboard.contents.keys():
                if hasattr(widget, 'get_nano_container'):
                    nano = widget.get_nano_container()
                    for attr in dir(nano):
                        if 'plotly' in attr.lower():
                            plotly_methods_found.append(attr)
        
        if plotly_methods_found:
            print(f"⚠️ Méthodes Plotly encore présentes: {plotly_methods_found}")
        else:
            print("✅ Aucune référence Plotly trouvée")
        
        # Vérifier le type de chart_generator
        if self.dashboard:
            for widget in self.dashboard.contents.keys():
                if hasattr(widget, 'get_nano_container'):
                    nano = widget.get_nano_container()
                    if hasattr(nano, 'chart_generator') and nano.chart_generator:
                        generator_type = type(nano.chart_generator).__name__
                        print(f"✅ Chart generator type: {generator_type}")
                        if 'Matplotlib' in generator_type:
                            print("✅ Matplotlib chart generator confirmé")
                        elif 'Plotly' in generator_type:
                            print("❌ Plotly chart generator encore présent!")
                        break

def test_final_fixes():
    """Lance le test des corrections finales"""
    app = QApplication(sys.argv)
    
    window = FinalFixesTestWindow()
    window.show()
    
    print("🎯 TEST CORRECTIONS FINALES")
    print("=" * 60)
    print("✅ CORRECTION 1: PLOTLY COMPLÈTEMENT SUPPRIMÉ")
    print("   • Toutes méthodes plot_*_plotly() supprimées")
    print("   • Matplotlib remplace Plotly")
    print("   • Graphiques intégrés DANS le dashboard")
    print("   • Plus d'ouverture navigateur web")
    print()
    print("✅ CORRECTION 2: EXPAND EN OVERLAY ABSOLU")
    print("   • Widget original caché (pas déplacé)")
    print("   • Overlay au niveau fenêtre principale")
    print("   • Container cloné dans overlay")
    print("   • Plus de poussée des autres widgets")
    print()
    print("🧪 TESTEZ LES CORRECTIONS:")
    print("   📊 Matplotlib: Graphiques dans dashboard")
    print("   ⛶ Expand: Overlay au premier plan")
    print("   🚫 Plotly: Vérification suppression")
    print()
    print("💡 Hover sur containers pour voir ⛶")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_final_fixes()
