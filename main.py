#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NanoMarketSensor - Application de surveillance des marchés de cryptomonnaies
"""

import sys
import os
import signal
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QObject, Signal, Slot, QTimer

# Ensure local imports work even if run from project root
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from nano_layout import MainWindow
from nano_processor import DataProcessor
from nano_database import MarketDatabase
from nano_tools import setup_logging, load_config as load_json_config

class Application(QObject):
    """Classe principale de l'application."""
    
    def __init__(self):
        super().__init__()
        self.app = QApplication(sys.argv)
        
        # Configuration initiale du logging (niveau minimal pour les erreurs de démarrage)
        import logging
        logging.basicConfig(level=logging.ERROR)
        self.logger = logging.getLogger('NanoMarketSensor')
        
        # Chargement de la configuration
        self.config = self.load_configuration()
        
        # Configuration finale du logging avec les paramètres de configuration
        self.logger = setup_logging(
            log_level=self.config.get('logging', {}).get('level', 'INFO'),
            log_file=self.config.get('logging', {}).get('file', 'nano_market_sensor.log')
        )
        
        # Initialisation de la base de données
        self.database = self.initialize_database()
        
        # Initialisation du processeur de données
        self.data_processor = DataProcessor(
            db_path=self.config['database']['path'],
            exchange_id=self.config['exchange']['id'],
            config=self.config['exchange'].get('config', {})
        )
        
        # Initialisation de l'interface utilisateur avec les dépendances
        self.main_window = MainWindow(
            config=self.config,
            database=self.database,
            data_processor=self.data_processor
        )
        
        # Configuration des gestionnaires de signaux
        self.setup_signal_handlers()
        
        # Configuration du gestionnaire de signaux système
        signal.signal(signal.SIGINT, self.handle_system_signal)
        signal.signal(signal.SIGTERM, self.handle_system_signal)
        
        # Timer pour vérifier les signaux système
        self.timer = QTimer()
        self.timer.timeout.connect(lambda: None)  # Juste pour garder la boucle d'événements active
        self.timer.start(200)  # Vérifier toutes les 200ms
    
    def load_configuration(self) -> Dict[str, Any]:
        """Charge la configuration de l'application."""
        # Configuration par défaut
        default_config = {
            'app': {
                'name': 'NanoMarketSensor',
                'version': '1.0.0',
                'dark_mode': True,
            },
            'database': {
                'path': 'market_data.db',  # Base de données dans le dossier du programme
            },
            'exchange': {
                'id': 'binance',
                'config': {
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'future',
                        'adjustForTimeDifference': True,
                    },
                },
            },
            'logging': {
                'level': 'INFO',
                'file': 'nano_market_sensor.log',  # Log dans le dossier du programme
            },
        }
        
        # Charger la configuration depuis le fichier
        config_path = Path('config.json')
        if config_path.exists():
            try:
                user_config = load_json_config(config_path)
                # Fusionner avec la configuration par défaut
                self.merge_configs(default_config, user_config)
                default_config = user_config  # Mettre à jour la config par défaut avec celle de l'utilisateur
            except Exception as e:
                self.logger.error(f"Error loading config: {str(e)}")
        
        return default_config
    
    def merge_configs(self, base: Dict, update: Dict, path: str = '') -> None:
        """Fusionne récursivement deux dictionnaires de configuration."""
        for key, value in update.items():
            new_path = f"{path}.{key}" if path else key
            
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self.merge_configs(base[key], value, new_path)
            else:
                if key in base:
                    base[key] = value
                else:
                    self.logger.warning(f"Unknown config key: {new_path}")
    
    def initialize_database(self) -> MarketDatabase:
        """Initialise la base de données."""
        try:
            db_config = self.config.get('database', {})
            db_path = Path(db_config.get('path', 'market_data.db'))
            
            # Créer le répertoire de la base de données s'il n'existe pas
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Vérifier si la base de données existe déjà et faire une sauvegarde si nécessaire
            if db_path.exists() and db_config.get('auto_backup', False):
                backup_dir = Path(db_config.get('backup_path', 'backups'))
                backup_dir.mkdir(parents=True, exist_ok=True)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = backup_dir / f"{db_path.stem}_{timestamp}{db_path.suffix}"
                import shutil
                shutil.copy2(db_path, backup_path)
                self.logger.info(f"Created database backup at: {backup_path}")
            
            # Initialiser la base de données
            db = MarketDatabase(str(db_path))
            self.logger.info("Database initialized successfully")
            
            return db
            
        except Exception as e:
            self.logger.critical(f"Error initializing database: {str(e)}", exc_info=True)
            QMessageBox.critical(
                None,
                "Erreur critique",
                f"Erreur lors de l'initialisation de la base de données :\n{str(e)}\n"
                "L'application va se terminer."
            )
            sys.exit(1)
    
    def setup_signal_handlers(self):
        """Configure les gestionnaires de signaux."""
        # Connecter les signaux de l'interface utilisateur
        self.main_window.destroyed.connect(self.cleanup)
        
        # Connecter les signaux du processeur de données
        if hasattr(self.data_processor, 'error'):
            self.data_processor.error.connect(self.on_processor_error)
            
        # Connecter le signal de progression à la fenêtre principale
        if hasattr(self.data_processor, 'progress_updated') and hasattr(self.main_window, 'update_status_progress'):
            self.data_processor.progress_updated.connect(self.main_window.update_status_progress)
            self.logger.debug("Signal de progression connecté avec succès")
    
    def on_processor_error(self, error_msg: str):
        """Gère les erreurs du processeur de données."""
        self.logger.error(f"Error processing data: {error_msg}")
        # Afficher une notification à l'utilisateur si l'interface est disponible
        if hasattr(self, 'main_window') and self.main_window.isVisible():
            QMessageBox.warning(
                self.main_window,
                "Data processing error",
                f"Error processing data: {error_msg}"
            )
    
    def handle_system_signal(self, signum, frame):
        """Gère les signaux système (Ctrl+C, kill, etc.)."""
        self.logger.info(f"Signal system {signum}, stop in progress...")
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """Nettoie les ressources avant la fermeture de l'application."""
        self.logger.info("Cleanup before application exit")
        
        # Arrêter le processeur de données
        if hasattr(self, 'data_processor') and self.data_processor:
            self.logger.info("Arrêt du processeur de données...")
            self.data_processor.stop()
            # Donner le temps aux threads de s'arrêter
            import time
            time.sleep(1)
        
        # Fermer la base de données (la méthode __del__ s'en charge automatiquement)
        if hasattr(self, 'database') and self.database:
            self.logger.info("Fermeture de la base de données...")
            self.database = None  # Déclenche __del__
        
        # Arrêter le timer système
        if hasattr(self, 'timer') and self.timer:
            self.timer.stop()
        
        # Fermer la fenêtre principale
        if hasattr(self, 'main_window') and self.main_window:
            self.logger.info("Fermeture de la fenêtre principale...")
            self.main_window.close()
            
        self.logger.info("Nettoyage terminé")
    
    def run(self) -> int:
        """Lance l'application."""
        try:
            self.logger.info("Application started")
            
            # Démarrer le processeur de données
            self.data_processor.start()
            
            # Afficher la fenêtre principale
            self.main_window.show()
            
            # Lancer la boucle d'événements
            return self.app.exec()
            
        except Exception as e:
            self.logger.critical(f"Fatal error: {str(e)}", exc_info=True)
            QMessageBox.critical(
                None,
                "Critical error",
                f"Fatal error:\n{str(e)}\n\n"
                "Application will exit."
            )
            return 1


def main():
    """Point d'entrée principal de l'application."""
    try:
        app = Application()
        return app.run()
    except Exception as e:
        print(f"Critical error: {str(e)}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
