#!/usr/bin/env python3
"""
Test des graphiques matplotlib intégrés dans le dashboard
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt
from nano_dashboard import NanoDashboard
from nano_style import get_test_button_style, get_test_info_style

class IntegratedChartsTestWindow(QMainWindow):
    """Fenêtre de test des graphiques intégrés"""
    
    def __init__(self):
        super().__init__()
        self.dark_mode = True
        self.setup_ui()
        self.setup_dashboard()
        
    def setup_ui(self):
        """Configure l'interface"""
        self.setWindowTitle("📊 Test Graphiques Intégrés - Matplotlib dans Dashboard")
        self.setGeometry(50, 50, 1400, 1000)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Contrôles
        controls_layout = QHBoxLayout()
        
        # Bouton toggle dark/light mode
        self.toggle_mode_btn = QPushButton("🌙 Mode Clair")
        self.toggle_mode_btn.clicked.connect(self.toggle_dark_mode)
        controls_layout.addWidget(self.toggle_mode_btn)
        
        # Bouton test line chart
        test_line_btn = QPushButton("📈 Test Line Chart")
        test_line_btn.clicked.connect(self.test_line_chart)
        controls_layout.addWidget(test_line_btn)
        
        # Bouton test candlestick
        test_candle_btn = QPushButton("🕯️ Test Candlestick")
        test_candle_btn.clicked.connect(self.test_candlestick)
        controls_layout.addWidget(test_candle_btn)
        
        # Bouton test dominance
        test_dom_btn = QPushButton("🔄 Test Dominance")
        test_dom_btn.clicked.connect(self.test_dominance)
        controls_layout.addWidget(test_dom_btn)
        
        # Bouton test volume
        test_vol_btn = QPushButton("📊 Test Volume")
        test_vol_btn.clicked.connect(self.test_volume)
        controls_layout.addWidget(test_vol_btn)
        
        # Bouton test correlation
        test_corr_btn = QPushButton("🔥 Test Correlation")
        test_corr_btn.clicked.connect(self.test_correlation)
        controls_layout.addWidget(test_corr_btn)
        
        controls_layout.addStretch()
        
        # Style des boutons
        button_style = get_test_button_style()
        for btn in [self.toggle_mode_btn, test_line_btn, test_candle_btn, test_dom_btn, test_vol_btn, test_corr_btn]:
            btn.setStyleSheet(button_style)
        
        main_layout.addLayout(controls_layout)
        
        # Dashboard
        self.dashboard = None
        
        # Info
        info_text = """
📊 GRAPHIQUES MATPLOTLIB INTÉGRÉS DANS LE DASHBOARD

✅ CORRECTION APPLIQUÉE:
   • Remplacement de Plotly par matplotlib
   • Graphiques affichés DIRECTEMENT dans le dashboard
   • Plus d'ouverture dans le navigateur web
   • Intégration native avec Qt/PySide6

🎯 TYPES DE GRAPHIQUES DISPONIBLES:
   • Line Chart: Évolution des prix
   • Candlestick: Graphiques OHLC
   • Dominance: BTC/ETH/Alt dominance
   • Volume: Barres de volume
   • Correlation: Heatmap de corrélation

💡 TESTEZ CHAQUE TYPE:
   • Cliquez sur les boutons pour générer des graphiques
   • Les graphiques s'affichent dans les containers
   • Mode sombre/clair adaptatif
   • Redimensionnement automatique
        """
        
        from PySide6.QtWidgets import QLabel
        info_label = QLabel(info_text)
        info_label.setStyleSheet(get_test_info_style())
        main_layout.addWidget(info_label)
        
    def setup_dashboard(self):
        """Configure le dashboard"""
        print("📊 Création du dashboard avec graphiques intégrés...")
        
        # Créer le dashboard
        self.dashboard = NanoDashboard(self.dark_mode)
        
        # Ajouter au layout
        self.centralWidget().layout().insertWidget(1, self.dashboard)
        
        print("✅ Dashboard créé avec graphiques matplotlib intégrés")
        
    def toggle_dark_mode(self):
        """Bascule entre mode sombre et clair"""
        self.dark_mode = not self.dark_mode
        
        if self.dark_mode:
            self.toggle_mode_btn.setText("🌙 Mode Clair")
        else:
            self.toggle_mode_btn.setText("☀️ Mode Sombre")
        
        if self.dashboard:
            self.dashboard.update_dark_mode(self.dark_mode)
        
        print(f"🎨 Mode basculé vers: {'Sombre' if self.dark_mode else 'Clair'}")
        
    def get_matplotlib_containers(self):
        """Récupère tous les containers matplotlib"""
        containers = []
        if self.dashboard:
            for widget in self.dashboard.contents.keys():
                if hasattr(widget, 'get_nano_container'):
                    nano = widget.get_nano_container()
                    if hasattr(nano, 'chart_generator') and nano.chart_generator:
                        containers.append(nano)
        return containers
        
    def test_line_chart(self):
        """Teste les graphiques linéaires"""
        print("📈 TEST LINE CHART:")
        print("=" * 30)
        
        containers = self.get_matplotlib_containers()
        if containers:
            container = containers[0]
            
            # Données de test
            x_data = list(range(50))
            y_data = [50000 + i*100 + (i%10)*500 for i in x_data]
            
            container.create_line_chart(x_data, y_data, "Bitcoin Price Evolution", "Time", "Price ($)")
            print(f"✅ Line chart créé dans container")
        else:
            print("❌ Aucun container matplotlib trouvé")
            
    def test_candlestick(self):
        """Teste les graphiques en chandelles"""
        print("🕯️ TEST CANDLESTICK:")
        print("=" * 30)
        
        containers = self.get_matplotlib_containers()
        if len(containers) > 1:
            container = containers[1]
            
            # Données vides pour utiliser les données par défaut
            import pandas as pd
            ohlcv_data = pd.DataFrame()
            
            container.create_candlestick_chart(ohlcv_data, "BTC/USDT Candlestick")
            print(f"✅ Candlestick chart créé dans container")
        else:
            print("❌ Pas assez de containers matplotlib")
            
    def test_dominance(self):
        """Teste les graphiques de dominance"""
        print("🔄 TEST DOMINANCE:")
        print("=" * 30)
        
        containers = self.get_matplotlib_containers()
        if len(containers) > 2:
            container = containers[2]
            
            # Données vides pour utiliser les données par défaut
            import pandas as pd
            dom_data = pd.DataFrame()
            
            container.create_dominance_chart(dom_data)
            print(f"✅ Dominance chart créé dans container")
        else:
            print("❌ Pas assez de containers matplotlib")
            
    def test_volume(self):
        """Teste les graphiques de volume"""
        print("📊 TEST VOLUME:")
        print("=" * 30)
        
        containers = self.get_matplotlib_containers()
        if len(containers) > 3:
            container = containers[3]
            
            # Données vides pour utiliser les données par défaut
            import pandas as pd
            vol_data = pd.DataFrame()
            
            container.create_volume_chart(vol_data)
            print(f"✅ Volume chart créé dans container")
        else:
            print("❌ Pas assez de containers matplotlib")
            
    def test_correlation(self):
        """Teste les heatmaps de corrélation"""
        print("🔥 TEST CORRELATION:")
        print("=" * 30)
        
        containers = self.get_matplotlib_containers()
        if len(containers) > 4:
            container = containers[4]
            
            # Données vides pour utiliser les données par défaut
            import pandas as pd
            corr_data = pd.DataFrame()
            
            container.create_correlation_heatmap(corr_data)
            print(f"✅ Correlation heatmap créée dans container")
        else:
            print("❌ Pas assez de containers matplotlib")

def test_integrated_charts():
    """Lance le test des graphiques intégrés"""
    app = QApplication(sys.argv)
    
    window = IntegratedChartsTestWindow()
    window.show()
    
    print("📊 TEST GRAPHIQUES INTÉGRÉS")
    print("=" * 60)
    print("✅ CORRECTION MAJEURE: Plotly → Matplotlib")
    print("   • Graphiques affichés DANS le dashboard")
    print("   • Plus d'ouverture navigateur web")
    print("   • Intégration native Qt/PySide6")
    print("   • Performance optimisée")
    print()
    print("🎯 TYPES DE GRAPHIQUES:")
    print("   📈 Line Chart: Évolution des prix")
    print("   🕯️ Candlestick: Graphiques OHLC")
    print("   🔄 Dominance: BTC/ETH/Alt")
    print("   📊 Volume: Barres de volume")
    print("   🔥 Correlation: Heatmap")
    print()
    print("💡 Testez chaque type avec les boutons")
    print("💡 Mode sombre/clair adaptatif")
    print("💡 Redimensionnement automatique")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_integrated_charts()
