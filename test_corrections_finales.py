#!/usr/bin/env python3
"""
Test final pour vérifier toutes les corrections appliquées
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PySide6.QtCore import Qt

from nano_dashboard import NanoDashboard

class TestCorrectionsFinalesWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 Test Final - Toutes les Corrections")
        self.setGeometry(50, 50, 1600, 1000)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # Titre
        title_label = QLabel("🧪 TEST FINAL - VÉRIFICATION DES CORRECTIONS")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; margin: 10px; color: #2ecc71;")
        main_layout.addWidget(title_label)
        
        # Boutons de test
        buttons_layout = QHBoxLayout()
        
        test_sizing_btn = QPushButton("📏 Test Dimensionnement")
        test_sizing_btn.clicked.connect(self.test_sizing)
        buttons_layout.addWidget(test_sizing_btn)
        
        test_titles_btn = QPushButton("📊 Test Titres Graphiques")
        test_titles_btn.clicked.connect(self.test_titles)
        buttons_layout.addWidget(test_titles_btn)
        
        test_new_containers_btn = QPushButton("🆕 Test Nouveaux Containers")
        test_new_containers_btn.clicked.connect(self.test_new_containers)
        buttons_layout.addWidget(test_new_containers_btn)
        
        test_all_btn = QPushButton("🎯 Test Complet")
        test_all_btn.clicked.connect(self.test_all)
        buttons_layout.addWidget(test_all_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # Dashboard
        self.dashboard = NanoDashboard(dark_mode=True)
        main_layout.addWidget(self.dashboard)
        
        # Résultats
        self.results_label = QLabel("💡 Cliquez sur les boutons pour tester les corrections")
        self.results_label.setAlignment(Qt.AlignCenter)
        self.results_label.setStyleSheet("color: #888; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px;")
        main_layout.addWidget(self.results_label)
        
        print("🧪 Test des corrections finales créé")
        
    def test_sizing(self):
        """Test du dimensionnement"""
        print("🧪 Test: Dimensionnement des containers")
        results = []
        
        # Vérifier que les containers s'adaptent
        for widget in self.dashboard.contents.keys():
            if hasattr(widget, 'nano_container') and widget.nano_container:
                size = widget.nano_container.size()
                if size.width() > 250 and size.height() > 200:
                    results.append("✅ Container bien dimensionné")
                else:
                    results.append(f"❌ Container trop petit: {size.width()}x{size.height()}")
        
        # Tester le redimensionnement
        original_size = self.size()
        self.resize(1200, 800)
        QApplication.processEvents()
        self.resize(original_size)
        QApplication.processEvents()
        
        results.append("✅ Test de redimensionnement effectué")
        
        self.results_label.setText("📏 DIMENSIONNEMENT:\n" + "\n".join(results[:5]))
        
    def test_titles(self):
        """Test des titres de graphiques"""
        print("🧪 Test: Titres des graphiques")
        results = []
        
        # Vérifier les nouveaux titres
        expected_titles = [
            "🟡 BTC Dominance",
            "🌀 Altseason Index", 
            "⚠️ Global Risk Index",
            "📊 Global Volume",
            "💰 Market Cap",
            "💸 Capital Flows",
            "📚 Orderbook Depth",
            "🔄 Vol vs Dominance"
        ]
        
        found_titles = []
        for widget in self.dashboard.contents.keys():
            if hasattr(widget, 'container_config'):
                title = widget.container_config.get('title', '')
                found_titles.append(title)
        
        for expected in expected_titles:
            if expected in found_titles:
                results.append(f"✅ {expected}")
            else:
                results.append(f"❌ Manque: {expected}")
        
        self.results_label.setText("📊 TITRES GRAPHIQUES:\n" + "\n".join(results[:6]))
        
    def test_new_containers(self):
        """Test des nouveaux containers"""
        print("🧪 Test: Nouveaux containers du reference.py")
        results = []
        
        # Vérifier que BTC/ETH ont été remplacés
        old_containers = ["₿ BTC/USDT", "Ξ ETH/USDT"]
        new_containers = ["🌀 Altseason Index", "⚠️ Global Risk Index", "💸 Capital Flows", "📚 Orderbook Depth"]
        
        found_titles = []
        for widget in self.dashboard.contents.keys():
            if hasattr(widget, 'container_config'):
                title = widget.container_config.get('title', '')
                found_titles.append(title)
        
        # Vérifier que les anciens containers ont été supprimés
        for old in old_containers:
            if old not in found_titles:
                results.append(f"✅ Supprimé: {old}")
            else:
                results.append(f"❌ Encore présent: {old}")
        
        # Vérifier que les nouveaux containers sont présents
        for new in new_containers:
            if new in found_titles:
                results.append(f"✅ Ajouté: {new}")
            else:
                results.append(f"❌ Manque: {new}")
        
        self.results_label.setText("🆕 NOUVEAUX CONTAINERS:\n" + "\n".join(results[:6]))
        
    def test_all(self):
        """Test complet de toutes les corrections"""
        print("🧪 Test: Vérification complète")
        results = []
        
        # 1. Dimensionnement
        sizing_ok = True
        for widget in self.dashboard.contents.keys():
            if hasattr(widget, 'nano_container') and widget.nano_container:
                size = widget.nano_container.size()
                if size.width() < 250 or size.height() < 200:
                    sizing_ok = False
                    break
        
        results.append("✅ Dimensionnement OK" if sizing_ok else "❌ Problème dimensionnement")
        
        # 2. Nouveaux containers
        found_titles = []
        for widget in self.dashboard.contents.keys():
            if hasattr(widget, 'container_config'):
                title = widget.container_config.get('title', '')
                found_titles.append(title)
        
        new_containers_present = all(title in found_titles for title in [
            "🌀 Altseason Index", "⚠️ Global Risk Index", "💸 Capital Flows"
        ])
        old_containers_removed = all(title not in found_titles for title in [
            "₿ BTC/USDT", "Ξ ETH/USDT"
        ])
        
        results.append("✅ Nouveaux containers OK" if new_containers_present else "❌ Nouveaux containers manquants")
        results.append("✅ Anciens containers supprimés" if old_containers_removed else "❌ Anciens containers présents")
        
        # 3. Grille 3x4
        total_containers = len(list(self.dashboard.contents.keys()))
        results.append(f"✅ 12 containers présents" if total_containers == 12 else f"❌ {total_containers} containers (attendu: 12)")
        
        # 4. Types d'analyse
        analysis_types = set()
        for widget in self.dashboard.contents.keys():
            if hasattr(widget, 'container_config'):
                analysis = widget.container_config.get('analysis', '')
                analysis_types.add(analysis)
        
        expected_analyses = {'dominance', 'altseason', 'risk', 'volume', 'marketcap', 'capital_flows', 'orderbook', 'vol_dominance'}
        new_analyses_present = len(expected_analyses.intersection(analysis_types)) >= 6
        
        results.append("✅ Nouvelles analyses OK" if new_analyses_present else "❌ Analyses manquantes")
        
        # Résultat final
        all_ok = all("✅" in result for result in results)
        final_status = "🎯 TOUTES LES CORRECTIONS APPLIQUÉES AVEC SUCCÈS !" if all_ok else "⚠️ Certaines corrections nécessitent attention"
        
        self.results_label.setText(final_status + "\n\n" + "\n".join(results))
        
        if all_ok:
            self.results_label.setStyleSheet("color: #2ecc71; margin: 10px; padding: 10px; background: #d5f4e6; border-radius: 5px; font-weight: bold;")
        else:
            self.results_label.setStyleSheet("color: #e74c3c; margin: 10px; padding: 10px; background: #fdf2f2; border-radius: 5px; font-weight: bold;")

def main():
    app = QApplication(sys.argv)
    
    # Style sombre
    app.setStyleSheet("""
        QMainWindow {
            background-color: #0d1117;
            color: #c9d1d9;
        }
        QPushButton {
            background-color: #21262d;
            color: #c9d1d9;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 120px;
        }
        QPushButton:hover {
            background-color: #30363d;
        }
        QPushButton:pressed {
            background-color: #484f58;
        }
    """)
    
    window = TestCorrectionsFinalesWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
