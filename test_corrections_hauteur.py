#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test des corrections de hauteur et du problème "Default Chart"
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from nano_content_widget import ContentWidgetWithNanoContainer, CONTAINER_CONFIGS

class TestWindow(QMainWindow):
    """Fenêtre de test pour vérifier les corrections"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Corrections - Hauteur et Default Chart")
        self.setGeometry(100, 100, 1200, 800)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Créer quelques containers de test
        self.test_containers = []
        
        # Test 1: Container avec graphique (devrait avoir un graphique par défaut)
        print("🧪 Test 1: Container avec graphique (dominance)")
        config_dominance = CONTAINER_CONFIGS[0]  # BTC Dominance
        container1 = ContentWidgetWithNanoContainer(
            dark_mode=True, 
            container_config=config_dominance
        )
        container1.setMaximumHeight(250)  # Hauteur fixe pour test
        layout.addWidget(container1)
        self.test_containers.append(("Dominance", container1))
        
        # Test 2: Container sans graphique par défaut (forecast)
        print("🧪 Test 2: Container sans graphique par défaut (forecast)")
        config_forecast = CONTAINER_CONFIGS[9]  # Price Forecast
        container2 = ContentWidgetWithNanoContainer(
            dark_mode=True, 
            container_config=config_forecast
        )
        container2.setMaximumHeight(250)  # Hauteur fixe pour test
        layout.addWidget(container2)
        self.test_containers.append(("Forecast", container2))
        
        # Test 3: Container sentiment (devrait afficher message d'attente)
        print("🧪 Test 3: Container sentiment (message d'attente)")
        config_sentiment = CONTAINER_CONFIGS[10]  # Market Sentiment
        container3 = ContentWidgetWithNanoContainer(
            dark_mode=True, 
            container_config=config_sentiment
        )
        container3.setMaximumHeight(250)  # Hauteur fixe pour test
        layout.addWidget(container3)
        self.test_containers.append(("Sentiment", container3))
        
        # Vérifier les résultats après un court délai
        from PySide6.QtCore import QTimer
        QTimer.singleShot(2000, self.verify_corrections)
    
    def verify_corrections(self):
        """Vérifie que les corrections ont été appliquées"""
        print("\n🔍 Vérification des corrections...")
        
        for name, container in self.test_containers:
            nano_container = container.get_nano_container()
            if nano_container:
                # Vérifier la hauteur
                height = nano_container.height()
                print(f"📏 {name}: Hauteur = {height}px")
                
                # Vérifier le type de container et le contenu
                if hasattr(nano_container, 'chart_generator'):
                    if hasattr(nano_container, 'show_default_chart'):
                        if nano_container.show_default_chart:
                            print(f"📊 {name}: Graphique par défaut activé ✅")
                        else:
                            print(f"⏳ {name}: Message d'attente activé ✅")
                    else:
                        print(f"⚠️ {name}: Attribut show_default_chart manquant")
                else:
                    print(f"📋 {name}: Container normal (pas de graphique)")
            else:
                print(f"❌ {name}: NanoContainer non trouvé")
        
        print("\n✅ Vérification terminée")

def main():
    """Fonction principale de test"""
    app = QApplication(sys.argv)
    
    print("🚀 Lancement du test des corrections...")
    print("📋 Tests à effectuer:")
    print("  1. Vérifier que la hauteur des containers ne dépasse pas le parent")
    print("  2. Vérifier que les containers 'forecast', 'sentiment', 'momentum' n'affichent pas 'Default Chart'")
    print("  3. Vérifier que les autres containers affichent bien un graphique par défaut")
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
