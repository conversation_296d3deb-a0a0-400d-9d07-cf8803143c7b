#!/usr/bin/env python3
"""
Test des corrections définitives :
1. Bouton expand en position absolue (pas dans layout)
2. Matplotlib sans erreur Plotly
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt
from nano_dashboard import NanoDashboard
from nano_style import get_test_button_style, get_test_info_style

class CorrectionsDefinitivesTestWindow(QMainWindow):
    """Fenêtre de test des corrections définitives"""
    
    def __init__(self):
        super().__init__()
        self.dark_mode = True
        self.setup_ui()
        self.setup_dashboard()
        
    def setup_ui(self):
        """Configure l'interface"""
        self.setWindowTitle("🎯 CORRECTIONS DÉFINITIVES - Expand Absolu + Matplotlib Sans Plotly")
        self.setGeometry(50, 50, 1400, 1000)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Contrôles
        controls_layout = QHBoxLayout()
        
        # Bouton toggle dark/light mode
        self.toggle_mode_btn = QPushButton("🌙 Mode Clair")
        self.toggle_mode_btn.clicked.connect(self.toggle_dark_mode)
        controls_layout.addWidget(self.toggle_mode_btn)
        
        # Bouton test expand position absolue
        test_expand_btn = QPushButton("⛶ Test Expand Position Absolue")
        test_expand_btn.clicked.connect(self.test_expand_position)
        controls_layout.addWidget(test_expand_btn)
        
        # Bouton test matplotlib
        test_matplotlib_btn = QPushButton("📊 Test Matplotlib")
        test_matplotlib_btn.clicked.connect(self.test_matplotlib_clean)
        controls_layout.addWidget(test_matplotlib_btn)
        
        # Bouton hover test
        hover_test_btn = QPushButton("👆 Test Hover")
        hover_test_btn.clicked.connect(self.test_hover)
        controls_layout.addWidget(hover_test_btn)
        
        controls_layout.addStretch()
        
        # Style des boutons
        button_style = get_test_button_style()
        for btn in [self.toggle_mode_btn, test_expand_btn, test_matplotlib_btn, hover_test_btn]:
            btn.setStyleSheet(button_style)
        
        main_layout.addLayout(controls_layout)
        
        # Dashboard
        self.dashboard = None
        
        # Info finale
        info_text = """
🎯 CORRECTIONS DÉFINITIVES APPLIQUÉES:

✅ CORRECTION 1: BOUTON EXPAND EN POSITION ABSOLUE
   • Bouton expand retiré du layout VBox
   • Position absolue en haut à droite
   • move(width-35, 5) au lieu d'addWidget()
   • resizeEvent() pour repositionnement
   • Plus de poussée des widgets vers le bas

✅ CORRECTION 2: MATPLOTLIB SANS PLOTLY
   • Import matplotlib avec gestion d'erreur robuste
   • Backend Qt5Agg forcé
   • Vérification MATPLOTLIB_IMPORTS_OK
   • Plus d'erreur "packaging.version"

🧪 TESTS DISPONIBLES:
   ⛶ Test Expand: Vérifier position absolue
   📊 Test Matplotlib: Vérifier graphiques intégrés
   👆 Test Hover: Vérifier apparition bouton

💡 RÉSULTAT ATTENDU:
   • Hover sur container → bouton ⛶ apparaît en haut à droite
   • Clic ⛶ → expand sans bouger les autres widgets
   • Graphiques matplotlib intégrés dans dashboard
   • Plus d'erreur Plotly/packaging
        """
        
        from PySide6.QtWidgets import QLabel
        info_label = QLabel(info_text)
        info_label.setStyleSheet(get_test_info_style())
        main_layout.addWidget(info_label)
        
    def setup_dashboard(self):
        """Configure le dashboard"""
        print("🎯 Création du dashboard avec corrections définitives...")
        
        # Créer le dashboard
        self.dashboard = NanoDashboard(self.dark_mode)
        
        # Ajouter au layout
        self.centralWidget().layout().insertWidget(1, self.dashboard)
        
        print("✅ Dashboard créé avec corrections définitives")
        
    def toggle_dark_mode(self):
        """Bascule entre mode sombre et clair"""
        self.dark_mode = not self.dark_mode
        
        if self.dark_mode:
            self.toggle_mode_btn.setText("🌙 Mode Clair")
        else:
            self.toggle_mode_btn.setText("☀️ Mode Sombre")
        
        if self.dashboard:
            self.dashboard.update_dark_mode(self.dark_mode)
        
        print(f"🎨 Mode basculé vers: {'Sombre' if self.dark_mode else 'Clair'}")
        
    def test_expand_position(self):
        """Teste la position absolue du bouton expand"""
        print("⛶ TEST EXPAND POSITION ABSOLUE:")
        print("=" * 40)
        
        if self.dashboard and self.dashboard.slot_order:
            # Analyser le premier container
            first_widget = self.dashboard.slot_order[0]
            
            print(f"✅ Container analysé: {type(first_widget).__name__}")
            
            # Vérifier la position du bouton
            if hasattr(first_widget, 'toggle_btn'):
                btn = first_widget.toggle_btn
                pos = btn.pos()
                size = first_widget.size()
                
                print(f"✅ Bouton trouvé à position: ({pos.x()}, {pos.y()})")
                print(f"✅ Taille container: {size.width()}x{size.height()}")
                print(f"✅ Position relative: {pos.x()}/{size.width()} = {pos.x()/size.width():.2%}")
                
                # Vérifier que le bouton est en position absolue (pas dans layout)
                parent_layout = first_widget.layout()
                btn_in_layout = False
                if parent_layout:
                    for i in range(parent_layout.count()):
                        item = parent_layout.itemAt(i)
                        if item and item.widget() == btn:
                            btn_in_layout = True
                            break
                
                if btn_in_layout:
                    print("❌ ERREUR: Bouton encore dans le layout!")
                else:
                    print("✅ SUCCÈS: Bouton en position absolue (pas dans layout)")
                
                # Tester l'expand
                print("💡 Test expand...")
                self.dashboard.expand_widget(first_widget)
                print("✅ Expand testé - vérifiez que les autres widgets ne bougent pas")
                
            else:
                print("❌ Bouton expand non trouvé")
        else:
            print("❌ Dashboard non disponible")
            
    def test_matplotlib_clean(self):
        """Teste matplotlib sans erreur Plotly"""
        print("📊 TEST MATPLOTLIB SANS PLOTLY:")
        print("=" * 40)
        
        try:
            # Test import direct
            from nano_matplotlib_charts import MatplotlibChartGenerator
            print("✅ Import MatplotlibChartGenerator réussi")
            
            # Test création
            generator = MatplotlibChartGenerator(True)
            print("✅ MatplotlibChartGenerator créé sans erreur")
            
            # Test génération graphique
            x_data = list(range(10))
            y_data = [i*100 for i in x_data]
            chart_widget = generator.create_line_chart(x_data, y_data, "Test Clean", "X", "Y")
            print("✅ Graphique matplotlib généré")
            
            if self.dashboard:
                matplotlib_containers = []
                for widget in self.dashboard.contents.keys():
                    if hasattr(widget, 'get_nano_container'):
                        nano = widget.get_nano_container()
                        if hasattr(nano, 'chart_generator') and nano.chart_generator:
                            matplotlib_containers.append(nano)
                
                print(f"✅ {len(matplotlib_containers)} containers matplotlib actifs")
                
                if matplotlib_containers:
                    container = matplotlib_containers[0]
                    container.create_line_chart(x_data, y_data, "Test Dashboard", "Time", "Value")
                    print("✅ Graphique affiché dans dashboard")
            
        except Exception as e:
            print(f"❌ Erreur matplotlib: {e}")
            import traceback
            traceback.print_exc()
            
    def test_hover(self):
        """Teste l'apparition du bouton au hover"""
        print("👆 TEST HOVER:")
        print("=" * 30)
        print("💡 Instructions:")
        print("1. Passez la souris sur n'importe quel container")
        print("2. Le bouton ⛶ doit apparaître en HAUT À DROITE")
        print("3. Le bouton doit être EN POSITION ABSOLUE")
        print("4. Cliquez sur ⛶ pour expand")
        print("5. Les autres containers ne doivent PAS bouger")
        print("6. Le container expandé doit apparaître au premier plan")

def test_corrections_definitives():
    """Lance le test des corrections définitives"""
    app = QApplication(sys.argv)
    
    window = CorrectionsDefinitivesTestWindow()
    window.show()
    
    print("🎯 TEST CORRECTIONS DÉFINITIVES")
    print("=" * 60)
    print("✅ CORRECTION 1: BOUTON EXPAND POSITION ABSOLUE")
    print("   • Bouton retiré du layout VBox")
    print("   • Position absolue avec move()")
    print("   • resizeEvent() pour repositionnement")
    print("   • Plus de poussée des widgets")
    print()
    print("✅ CORRECTION 2: MATPLOTLIB SANS PLOTLY")
    print("   • Import matplotlib robuste")
    print("   • Backend Qt5Agg forcé")
    print("   • Plus d'erreur packaging.version")
    print("   • Graphiques intégrés dashboard")
    print()
    print("🧪 TESTEZ LES CORRECTIONS:")
    print("   ⛶ Expand: Position absolue")
    print("   📊 Matplotlib: Sans erreur Plotly")
    print("   👆 Hover: Apparition bouton")
    print()
    print("💡 Hover sur containers pour voir ⛶ en haut à droite")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_corrections_definitives()
