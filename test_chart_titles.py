#!/usr/bin/env python3
"""
Test pour vérifier que les graphiques ont des titres différents
et que les containers s'adaptent bien au parent
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PySide6.QtCore import Qt

from nano_container import MatplotlibChartContainer
import pandas as pd
import numpy as np

class TestChartTitlesWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 Test - Titres des Graphiques et Dimensionnement")
        self.setGeometry(100, 100, 1200, 800)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # Titre
        title_label = QLabel("🧪 Test des Titres de Graphiques et Dimensionnement")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # Boutons de test
        buttons_layout = QHBoxLayout()
        
        test_dominance_btn = QPushButton("📊 Test Dominance Chart")
        test_dominance_btn.clicked.connect(self.test_dominance_chart)
        buttons_layout.addWidget(test_dominance_btn)
        
        test_price_btn = QPushButton("₿ Test BTC Price Chart")
        test_price_btn.clicked.connect(self.test_price_chart)
        buttons_layout.addWidget(test_price_btn)
        
        test_volume_btn = QPushButton("📈 Test Volume Chart")
        test_volume_btn.clicked.connect(self.test_volume_chart)
        buttons_layout.addWidget(test_volume_btn)
        
        test_marketcap_btn = QPushButton("💰 Test Market Cap Chart")
        test_marketcap_btn.clicked.connect(self.test_marketcap_chart)
        buttons_layout.addWidget(test_marketcap_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # Container de test
        self.test_container = MatplotlibChartContainer(self, dark_mode=True)
        main_layout.addWidget(self.test_container)
        
        # Info
        info_label = QLabel("💡 Cliquez sur les boutons pour tester différents types de graphiques")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #888; margin: 10px;")
        main_layout.addWidget(info_label)
        
        print("🧪 Test window créée - Testez les différents types de graphiques")
        
    def test_dominance_chart(self):
        """Test du graphique de dominance"""
        print("🧪 Test: Graphique de dominance")
        
        # Créer des données de test
        dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
        btc_dom = 57 + np.random.randn(100) * 2
        eth_dom = 18 + np.random.randn(100) * 1
        alt_dom = 100 - btc_dom - eth_dom
        
        dominance_data = pd.DataFrame({
            'created_at': dates,
            'btc_dom': btc_dom,
            'eth_dom': eth_dom,
            'alt_dom': alt_dom
        })
        
        self.test_container.create_dominance_chart(dominance_data)
        self.test_container.set_banner_status("bullish", "🟡 BTC Dominance Test")
        self.test_container.set_phrase("Test du graphique de dominance avec titre spécifique")
        
    def test_price_chart(self):
        """Test du graphique de prix BTC"""
        print("🧪 Test: Graphique de prix BTC")
        
        # Créer des données de prix
        price_data = pd.Series([50000 + i*100 + np.random.randn()*500 for i in range(100)])
        
        self.test_container.create_price_chart(price_data, "BTC", "Bitcoin Price Evolution")
        self.test_container.set_banner_status("bullish", "₿ BTC Price Test")
        self.test_container.set_phrase("Test du graphique de prix BTC avec titre spécifique")
        
    def test_volume_chart(self):
        """Test du graphique de volume"""
        print("🧪 Test: Graphique de volume")
        
        # Créer des données de volume
        dates = pd.date_range(start='2024-01-01', periods=50, freq='h')
        volumes = np.random.exponential(50000000000, 50)
        
        volume_data = pd.DataFrame({
            'created_at': dates,
            'vol_global': volumes
        })
        
        self.test_container.create_volume_chart(volume_data)
        self.test_container.set_banner_status("neutral", "📈 Volume Test")
        self.test_container.set_phrase("Test du graphique de volume avec titre spécifique")
        
    def test_marketcap_chart(self):
        """Test du graphique de market cap"""
        print("🧪 Test: Graphique de market cap")
        
        # Créer des données de market cap
        dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
        mc_values = 3.5e12 + np.cumsum(np.random.randn(100) * 1e10)
        
        marketcap_data = pd.DataFrame({
            'created_at': dates,
            'mc_global': mc_values
        })
        
        self.test_container.create_marketcap_chart(marketcap_data)
        self.test_container.set_banner_status("bullish", "💰 Market Cap Test")
        self.test_container.set_phrase("Test du graphique de market cap avec titre spécifique")

def main():
    app = QApplication(sys.argv)
    
    # Style sombre
    app.setStyleSheet("""
        QMainWindow {
            background-color: #0d1117;
            color: #c9d1d9;
        }
        QPushButton {
            background-color: #21262d;
            color: #c9d1d9;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #30363d;
        }
        QPushButton:pressed {
            background-color: #484f58;
        }
    """)
    
    window = TestChartTitlesWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
