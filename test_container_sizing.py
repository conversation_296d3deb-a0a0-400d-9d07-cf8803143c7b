#!/usr/bin/env python3
"""
Test pour vérifier le dimensionnement des containers
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QPushButton, QHBoxLayout, QLabel, QSplitter)
from PySide6.QtCore import Qt

from nano_container import MatplotlibChartContainer

class TestContainerSizingWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 Test - Dimensionnement des Containers")
        self.setGeometry(100, 100, 1400, 900)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # Titre
        title_label = QLabel("🧪 Test du Dimensionnement des Containers")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # Boutons de contrôle
        controls_layout = QHBoxLayout()
        
        resize_btn = QPushButton("📏 Redimensionner Fenêtre")
        resize_btn.clicked.connect(self.test_resize)
        controls_layout.addWidget(resize_btn)
        
        small_btn = QPushButton("📱 Petite Taille")
        small_btn.clicked.connect(lambda: self.resize(800, 600))
        controls_layout.addWidget(small_btn)
        
        medium_btn = QPushButton("💻 Taille Moyenne")
        medium_btn.clicked.connect(lambda: self.resize(1200, 800))
        controls_layout.addWidget(medium_btn)
        
        large_btn = QPushButton("🖥️ Grande Taille")
        large_btn.clicked.connect(lambda: self.resize(1600, 1000))
        controls_layout.addWidget(large_btn)
        
        main_layout.addLayout(controls_layout)
        
        # Splitter pour tester le redimensionnement
        splitter = QSplitter(Qt.Horizontal)
        
        # Container 1
        self.container1 = MatplotlibChartContainer(self, dark_mode=True)
        self.container1.set_banner_status("bullish", "📊 Container 1")
        self.container1.set_phrase("Container de test - doit s'adapter à la taille")
        self.container1.create_dominance_chart(None)  # Données par défaut
        splitter.addWidget(self.container1)
        
        # Container 2
        self.container2 = MatplotlibChartContainer(self, dark_mode=True)
        self.container2.set_banner_status("neutral", "📈 Container 2")
        self.container2.set_phrase("Deuxième container - test de redimensionnement")
        self.container2.create_volume_chart(None)  # Données par défaut
        splitter.addWidget(self.container2)
        
        # Container 3
        self.container3 = MatplotlibChartContainer(self, dark_mode=True)
        self.container3.set_banner_status("bearish", "💰 Container 3")
        self.container3.set_phrase("Troisième container - adaptation automatique")
        self.container3.create_marketcap_chart(None)  # Données par défaut
        splitter.addWidget(self.container3)
        
        main_layout.addWidget(splitter)
        
        # Info
        info_label = QLabel("💡 Redimensionnez la fenêtre ou utilisez les boutons pour tester l'adaptation")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #888; margin: 10px;")
        main_layout.addWidget(info_label)
        
        print("🧪 Test de dimensionnement créé")
        print("📏 Taille initiale de la fenêtre:", self.size().width(), "x", self.size().height())
        
    def test_resize(self):
        """Test de redimensionnement automatique"""
        import time
        
        print("🧪 Test de redimensionnement automatique...")
        
        # Séquence de redimensionnements
        sizes = [
            (600, 400),   # Très petit
            (800, 600),   # Petit
            (1200, 800),  # Moyen
            (1600, 1000), # Grand
            (1400, 900)   # Retour normal
        ]
        
        for i, (width, height) in enumerate(sizes):
            print(f"📏 Redimensionnement {i+1}/5: {width}x{height}")
            self.resize(width, height)
            QApplication.processEvents()
            time.sleep(1)
        
        print("✅ Test de redimensionnement terminé")
        
    def resizeEvent(self, event):
        """Événement de redimensionnement"""
        super().resizeEvent(event)
        size = event.size()
        print(f"📏 Fenêtre redimensionnée: {size.width()}x{size.height()}")
        
        # Vérifier les tailles des containers
        if hasattr(self, 'container1'):
            c1_size = self.container1.size()
            c2_size = self.container2.size()
            c3_size = self.container3.size()
            print(f"   Container 1: {c1_size.width()}x{c1_size.height()}")
            print(f"   Container 2: {c2_size.width()}x{c2_size.height()}")
            print(f"   Container 3: {c3_size.width()}x{c3_size.height()}")

def main():
    app = QApplication(sys.argv)
    
    # Style sombre
    app.setStyleSheet("""
        QMainWindow {
            background-color: #0d1117;
            color: #c9d1d9;
        }
        QPushButton {
            background-color: #21262d;
            color: #c9d1d9;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #30363d;
        }
        QPushButton:pressed {
            background-color: #484f58;
        }
        QSplitter::handle {
            background-color: #30363d;
        }
    """)
    
    window = TestContainerSizingWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
