# nano_style.py (minimal)
from PySide6.QtGui import QPixmap, QIcon
from io import BytesIO
from PIL import Image

# Couleurs principales
PRIMARY = "#8F5AFF"

# Couleurs pour le thème clair/sombre
DARK_BG = "#2E3440"
LIGHT_BG = "#F5F7FA"
TEXT_DARK = "#D8DEE9"
TEXT_LIGHT = "#2E3440"

# Couleurs pour les widgets de contenu
CONTENT_DARK_BG = "#3B4252"      # Plus clair que le fond principal
CONTENT_LIGHT_BG = "#FFFFFF"     # Blanc pour contraster avec le fond clair
CONTENT_DARK_BORDER = "#4C566A"
CONTENT_LIGHT_BORDER = "#dadde3"
CONTENT_HOVER_DARK = "#434C5E"
CONTENT_HOVER_LIGHT = "#E5E9F0"

# Couleurs pour les barres de défilement
SCROLLBAR_DARK_BG = "#3B4252"
SCROLLBAR_LIGHT_BG = "#E5E9F0"
SCROLLBAR_DARK_HANDLE = "#4C566A"
SCROLLBAR_LIGHT_HANDLE = "#D8DEE9"
SCROLLBAR_DARK_HOVER = "#81A1C1"
SCROLLBAR_LIGHT_HOVER = "#5E81AC"

# Ombres
SHADOW_DARK = "0 2px 8px rgba(0, 0, 0, 0.25)"
SHADOW_LIGHT = "0 2px 8px rgba(0, 0, 0, 0.1)"

# Dimensions et espacements
BORDER_RADIUS_SM = "4px"       # Petits éléments comme les boutons
BORDER_RADIUS_MD = "8px"       # Widgets de contenu
BORDER_RADIUS_LG = "12px"      # Grands éléments comme les cartes

# Tailles de bordure
BORDER_WIDTH_XS = "1px"        # Bordures très fines
BORDER_WIDTH_SM = "2px"        # Bordures fines
BORDER_WIDTH_MD = "3px"        # Bordures moyennes

# Espacements (padding)
PADDING_XS = "2px"             # Très petit espacement
PADDING_SM = "4px"             # Petit espacement
PADDING_MD = "8px"             # Espacement moyen
PADDING_LG = "12px"            # Grand espacement

# Couleurs pour la fenêtre logs terminal
LOGS_TERMINAL_BG = "#0d1117"           # Fond terminal GitHub
LOGS_TERMINAL_TEXT = "#c9d1d9"         # Texte terminal
LOGS_TERMINAL_BORDER = "#30363d"       # Bordure terminal
LOGS_TERMINAL_SELECTION = "#264f78"    # Sélection terminal

# Couleurs pour les logs par niveau
LOGS_ERROR_COLOR = "#ff6b6b"           # Rouge pour ERROR
LOGS_WARNING_COLOR = "#ffa726"         # Orange pour WARNING
LOGS_INFO_COLOR = "#66bb6a"            # Vert pour INFO
LOGS_DEBUG_COLOR = "#90a4ae"           # Gris pour DEBUG

# Couleurs pour les boutons d'action logs
LOGS_BTN_REFRESH = "#4CAF50"           # Vert pour refresh
LOGS_BTN_AUTO_REFRESH = "#FF9800"      # Orange pour auto-refresh
LOGS_BTN_SAVE = "#2196F3"              # Bleu pour save
LOGS_BTN_CLEAR = "#F44336"             # Rouge pour clear
LOGS_BTN_SHARE = "#9C27B0"             # Violet pour share
LOGS_BTN_PRINT = "#607D8B"             # Gris-bleu pour print
LOGS_BTN_BACK = "#666666"              # Gris pour retour

# Couleurs pour les containers d'analyse
CONTAINER_CHART_BG = "#1e1e1e"         # Fond zone graphique
CONTAINER_TABLE_BG = "#2a2a2a"         # Fond tableau variations
CONTAINER_BANNER_BULLISH = "#2ecc71"   # Vert pour bullish
CONTAINER_BANNER_BEARISH = "#e74c3c"   # Rouge pour bearish
CONTAINER_BANNER_NEUTRAL = "#f39c12"   # Orange pour neutral
CONTAINER_PHRASE_BG = "#34495e"        # Fond phrase d'analyse

# Couleurs d'accent pour sélections
ACCENT_DARK = "#0078d4"                # Bleu accent mode sombre
ACCENT_LIGHT = "#106ebe"               # Bleu accent mode clair
PADDING_XL = "16px"            # Très grand espacement

# Marges
MARGIN_XS = "2px"              # Très petite marge
MARGIN_SM = "4px"              # Petite marge
MARGIN_MD = "8px"              # Marge moyenne
MARGIN_LG = "12px"             # Grande marge
MARGIN_XL = "16px"             # Très grande marge

# Tailles spécifiques
SCROLLBAR_WIDTH = "6px"       # Largeur des barres de défilement
SCROLLBAR_HANDLE_MIN_SIZE = "30px"  # Taille minimale du curseur de défilement


def get_global_brand_styles(dark: bool) -> str:
    bg = DARK_BG if dark else LIGHT_BG
    txt = TEXT_DARK if dark else TEXT_LIGHT
    base = f"""
    * {{ font-family: Helvetica; color: {txt}; }}
    QWidget {{ background-color: {bg}; }}
    """
    # Ajouter le style des icônes du top bar
    return base + get_top_icons_style(dark)


def get_nav_button_style(dark: bool, checked: bool, obj_name: str) -> str:
    # Logique corrigée pour le texte :
    # - Si checked : toujours blanc (car fond coloré)
    # - Si non-checked : blanc en dark mode, noir en light mode
    txt = "#ffffff" if checked else ("#ffffff" if dark else "#000000")
    base_bg = "transparent"
    hover_bg = "rgba(255,255,255,0.10)" if dark else "rgba(0,0,0,0.08)"
    active_bg = PRIMARY  # active button color (plein)

    bg = active_bg if checked else base_bg

    return f"""
    QPushButton#{obj_name} {{
        background-color: {bg};
        color: {txt};
        border: none;
        text-align: left;
        padding: {PADDING_MD} {PADDING_LG};
        padding-left: 18px;
        margin-top: {MARGIN_SM};
        margin-bottom: {MARGIN_SM};
        border-radius: {BORDER_RADIUS_LG};
        
    }}

    QPushButton#{obj_name}:hover {{
        background-color: {hover_bg};
    }}

    /* Hover sur actif garde la couleur active */
    QPushButton#{obj_name}[checked="true"]:hover {{
        background-color: {active_bg};
    }}
    """


def get_content_style(dark: bool) -> str:
    # Styles de base
    bg = CONTENT_DARK_BG if dark else CONTENT_LIGHT_BG
    border_color = CONTENT_DARK_BORDER if dark else CONTENT_LIGHT_BORDER
    hover_bg = CONTENT_HOVER_DARK if dark else CONTENT_HOVER_LIGHT
    
    # Style avec f-string pour une meilleure lisibilité
    # Utilisation de la syntaxe QWidget#objectName pour cibler précisément le widget
    style = f"""
    QWidget#content-widget {{
        background-color: {bg};
        border: {BORDER_WIDTH_XS} solid {border_color};
        border-radius: {BORDER_RADIUS_MD};
        padding: {PADDING_LG};
        margin-bottom: {MARGIN_MD};
    }}
    
    QWidget#content-widget:hover {{
        background-color: {hover_bg};
       
    }}
    """
    
    return style


# Style addition for top icon buttons hover rounding
def get_top_icons_style(dark: bool) -> str:
    # Ne change pas le style de base; applique seulement un border-radius au hover
    hover_bg = "rgba(255,255,255,0.08)" if dark else "rgba(0,0,0,0.06)"
    return (
        "QToolButton#topIconBtn:hover {"
        "  border-radius: 3px;"
        f"  background-color: {hover_bg};"
        "}"
    )


def get_scrollbar_style(dark: bool) -> str:
    """Retourne le style pour les barres de défilement."""
    bg = SCROLLBAR_DARK_BG if dark else SCROLLBAR_LIGHT_BG
    handle = SCROLLBAR_DARK_HANDLE if dark else SCROLLBAR_LIGHT_HANDLE
    hover = SCROLLBAR_DARK_HOVER if dark else SCROLLBAR_LIGHT_HOVER
    
    return f"""
    QScrollBar:vertical {{
        border: none;
        background: {bg};
        width: {SCROLLBAR_WIDTH};
        margin: 0;
    }}
    
    QScrollBar::handle:vertical {{
        background: {handle};
        min-height: {SCROLLBAR_HANDLE_MIN_SIZE};
        border-radius: {BORDER_RADIUS_SM};
    }}
    
    QScrollBar::handle:vertical:hover {{
        background: {hover};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0;
        background: none;
    }}
    
    QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
        background: none;
    }}
    
    /* Style horizontal pour compléter */
    QScrollBar:horizontal {{
        border: none;
        background: {bg};
        height: {SCROLLBAR_WIDTH};
        margin: 0;
    }}
    
    QScrollBar::handle:horizontal {{
        background: {handle};
        min-width: {SCROLLBAR_HANDLE_MIN_SIZE};
        border-radius: {BORDER_RADIUS_SM};
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background: {hover};
    }}
    """


def get_statusbar_style(dark: bool) -> str:
    """Retourne le style pour la barre de statut."""
    bg = DARK_BG if dark else LIGHT_BG
    border_color = CONTENT_DARK_BORDER if dark else CONTENT_LIGHT_BORDER
    text_color = TEXT_DARK if dark else TEXT_LIGHT
    
    return f"""
    QStatusBar {{
        background-color: {bg};
        color: {text_color};
        border: 1px solid {border_color};
        padding: 8px;
        margin-bottom: 4px;
        border-radius: {BORDER_RADIUS_SM};
        
    }}
    QStatusBar::item {{
        border: none;
        padding: 2px;
        margin: 2px;
    }}
    QStatusBar QLabel {{
        color: {text_color};
        background: transparent;
        padding: 2px 4px;
        margin: 2px;
    }}
"""


def get_recolored_qicon(path: str, color: tuple[int, int, int]) -> QIcon:
    try:
        img = Image.open(path).convert("RGBA")
    except Exception:
        return QIcon(path)
    data = img.getdata()
    new = [(*color, p[3]) if p[3] > 0 else p for p in data]
    img.putdata(new)
    buf = BytesIO(); img.save(buf, format='PNG')
    pix = QPixmap(); pix.loadFromData(buf.getvalue())
    return QIcon(pix)

# === STYLES POUR LA FENÊTRE LOGS ===

def get_logs_window_style(dark_mode: bool) -> str:
    """Retourne le style pour la fenêtre des logs"""
    bg_color = DARK_BG if dark_mode else LIGHT_BG
    text_color = TEXT_DARK if dark_mode else TEXT_LIGHT

    return f"""
    QWidget {{
        background-color: {bg_color};
        color: {text_color};
    }}
    """

def get_logs_terminal_style() -> str:
    """Retourne le style pour la zone de texte terminal des logs"""
    return f"""
    QTextEdit {{
        background-color: {LOGS_TERMINAL_BG};
        color: {LOGS_TERMINAL_TEXT};
        border: {BORDER_WIDTH_SM} solid {LOGS_TERMINAL_BORDER};
        border-radius: {BORDER_RADIUS_MD};
        padding: {PADDING_LG};
        selection-background-color: {LOGS_TERMINAL_SELECTION};
        font-family: "Courier New", monospace;
        font-size: 11px;
    }}
    QScrollBar:vertical {{
        background-color: #21262d;
        width: 12px;
        border-radius: 6px;
    }}
    QScrollBar::handle:vertical {{
        background-color: #484f58;
        border-radius: 6px;
        min-height: 20px;
    }}
    QScrollBar::handle:vertical:hover {{
        background-color: #5a6169;
    }}
    """

def get_logs_header_style(dark_mode: bool) -> str:
    """Retourne le style pour l'en-tête des logs"""
    return f"""
    QLabel {{
        font-size: 18px;
        font-weight: bold;
        color: {LOGS_INFO_COLOR};
        background-color: {DARK_BG if dark_mode else LIGHT_BG};
        padding: {PADDING_MD} {PADDING_LG};
        border-radius: {BORDER_RADIUS_SM};
        border: {BORDER_WIDTH_XS} solid {CONTENT_DARK_BORDER if dark_mode else CONTENT_LIGHT_BORDER};
    }}
    """

def get_logs_info_style(dark_mode: bool) -> str:
    """Retourne le style pour les infos du fichier de logs"""
    return f"""
    QLabel {{
        font-size: 12px;
        color: {SCROLLBAR_DARK_HANDLE if dark_mode else SCROLLBAR_LIGHT_HANDLE};
        background-color: {CONTENT_DARK_BG if dark_mode else CONTENT_LIGHT_BG};
        padding: {PADDING_SM} {PADDING_MD};
        border-radius: {BORDER_RADIUS_SM};
    }}
    """

def get_logs_button_style(button_type: str) -> str:
    """Retourne le style pour les boutons des logs"""

    # Couleurs selon le type de bouton
    colors = {
        'refresh': LOGS_BTN_REFRESH,
        'auto_refresh': LOGS_BTN_AUTO_REFRESH,
        'save': LOGS_BTN_SAVE,
        'clear': LOGS_BTN_CLEAR,
        'share': LOGS_BTN_SHARE,
        'print': LOGS_BTN_PRINT,
        'back': LOGS_BTN_BACK
    }

    base_color = colors.get(button_type, LOGS_BTN_BACK)

    # Fonction pour assombrir une couleur
    def darken_color(color: str, factor: float = 0.2) -> str:
        try:
            color = color.strip().lstrip('#')
            if len(color) != 6 or not all(c in '0123456789ABCDEFabcdef' for c in color):
                return "#333333"
            rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
            darkened = tuple(int(c * (1 - factor)) for c in rgb)
            return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
        except:
            return "#333333"

    hover_color = darken_color(base_color, 0.2)
    pressed_color = darken_color(base_color, 0.3)

    return f"""
    QPushButton {{
        background-color: {base_color};
        color: white;
        border: none;
        padding: {PADDING_MD} {PADDING_LG};
        border-radius: {BORDER_RADIUS_SM};
        font-weight: bold;
        font-size: 12px;
    }}
    QPushButton:hover {{
        background-color: {hover_color};
    }}
    QPushButton:pressed {{
        background-color: {pressed_color};
    }}
    """

# === STYLES POUR LES CONTAINERS D'ANALYSE ===

def get_container_style(dark_mode: bool = True) -> str:
    """Retourne le style pour un container d'analyse avec background transparent"""
    # Le paramètre dark_mode est gardé pour compatibilité mais non utilisé
    # car le background est transparent
    return """
    QWidget {
        background-color: transparent;
        border: none;
        margin: 0px;
        padding: 0px;
    }
    """

def get_chart_area_style(dark_mode: bool) -> str:
    """Retourne le style pour la zone graphique"""
    bg_color = CONTAINER_CHART_BG if dark_mode else "#f8f9fa"
    border_color = CONTENT_DARK_BORDER if dark_mode else CONTENT_LIGHT_BORDER

    return f"""
    QWidget {{
        background-color: Transparent !important;
        border: {BORDER_WIDTH_XS} solid {border_color};
        border-radius: {BORDER_RADIUS_SM};
        padding: {PADDING_MD};
        font-size: 10px;
    }}
    """

def get_variation_table_style(dark_mode: bool) -> str:
    """Retourne le style pour le tableau des variations"""
    bg_color = CONTAINER_TABLE_BG if dark_mode else "#ffffff"
    text_color = TEXT_DARK if dark_mode else TEXT_LIGHT
    border_color = CONTENT_DARK_BORDER if dark_mode else CONTENT_LIGHT_BORDER

    return f"""
    QTableWidget {{
        background-color: Transparent !important;
        color: {text_color};
        border: {BORDER_WIDTH_XS} solid {border_color};
        border-radius: {BORDER_RADIUS_SM};
        gridline-color: {border_color};
        font-size: 11px;
    }}
    QTableWidget::item {{
        padding: {PADDING_SM};
        border-bottom: 1px solid {border_color};
    }}
    QTableWidget::item:selected {{
        background-color: {ACCENT_DARK if dark_mode else ACCENT_LIGHT};
    }}
    QHeaderView::section {{
        background-color: Transparent !important;
        color: {text_color};
        padding: {PADDING_SM};
        border: none;
        font-weight: bold;
    }}
    """

def get_banner_style(status: str = "neutral") -> str:
    """Retourne le style pour les banners selon le statut"""
    colors = {
        "bullish": CONTAINER_BANNER_BULLISH,
        "bearish": CONTAINER_BANNER_BEARISH,
        "neutral": CONTAINER_BANNER_NEUTRAL
    }

    bg_color = colors.get(status, CONTAINER_BANNER_NEUTRAL)

    return f"""
    QLabel {{
        background-color: {bg_color};
        color: white;
        padding: {PADDING_MD};
        margin: {PADDING_SM};
        border-radius: {BORDER_RADIUS_SM};
        font-weight: bold;
        font-size: 14px;
        text-align: center;
    }}
    """

def get_phrase_style(dark_mode: bool) -> str:
    """Retourne le style pour les phrases d'analyse"""
    bg_color = CONTAINER_PHRASE_BG if dark_mode else "#ecf0f1"
    text_color = TEXT_DARK if dark_mode else TEXT_LIGHT

    return f"""
    QLabel {{
        background-color: Transparent !important;
        color: {text_color};
        padding: {PADDING_MD};
        margin: {PADDING_SM};
        border-radius: {BORDER_RADIUS_SM};
        font-style: italic;
        font-size: 12px;
    }}
    """

def get_chart_fallback_style(dark_mode: bool) -> str:
    """Retourne le style pour les graphiques de fallback"""
    bg_color = '#0d1117' if dark_mode else '#f8f9fa'
    text_color = '#c9d1d9' if dark_mode else '#333333'
    border_color = '#30363d' if dark_mode else '#dee2e6'

    return f"""
    QLabel {{
        background-color: Transparent !important;
        color: {text_color};
        border: 1px solid {border_color};
        border-radius: 8px;
        padding: 20px;
        font-size: 14px;
        min-height: 200px;
    }}
    """

def get_chart_info_style(dark_mode: bool) -> str:
    """Retourne le style pour les labels d'information des graphiques"""
    bg_color = '#0d1117' if dark_mode else '#f8f9fa'
    text_color = '#c9d1d9' if dark_mode else '#333333'
    border_color = '#30363d' if dark_mode else '#dee2e6'

    return f"""
    QLabel {{
        background-color: Transparent !important;
        color: {text_color};
        border: 1px solid {border_color};
        border-radius: 8px;
        padding: 15px;
        font-size: 12px;
        min-height: 100px;
    }}
    """

def get_test_button_style() -> str:
    """Retourne le style pour les boutons de test"""
    return """
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        margin: 2px;
    }
    QPushButton:hover {
        background-color: #2980b9;
    }
    """

def get_test_info_style() -> str:
    """Retourne le style pour les labels d'information de test"""
    return """
    QLabel {
        background-color: #ecf0f1;
        color: #2c3e50;
        padding: 10px;
        border-radius: 5px;
        font-size: 11px;
        margin: 10px 0;
    }
    """
