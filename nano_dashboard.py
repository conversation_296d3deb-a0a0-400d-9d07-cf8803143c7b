from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QScrollArea,
    QTextEdit,
    QFrame,
    QPushButton,
    QApplication,
    QStatusBar,
    QLabel,
)
from PySide6.QtCore import Qt, QMimeData
from PySide6.QtGui import QDrag, QFont


class ContentWidget(QWidget):
    """Slot container supporting dark mode, drag & drop, and expand/collapse."""

    def __init__(self, dark_mode=True, parent=None):
        super().__init__(parent)
        self.dark_mode = dark_mode
        self.drag_start_position = None
        self._is_collapsed = False
        self._original_pos = None  # (row, col)
        
        # Définir un ID unique pour le widget
        self.setObjectName('content-widget')
        
        # Activer les attributs de style nécessaires
        self.setAttribute(Qt.WA_StyledBackground, True)
        self.setAttribute(Qt.WA_TranslucentBackground, False)

        lay = QVBoxLayout(self)
        lay.setContentsMargins(8, 8, 8, 8)
        lay.setSpacing(8)

        # Header with expand/collapse button (no text labels)
        self.header = QHBoxLayout()
        self.toggle_btn = QPushButton("⛶", self)
        self.toggle_btn.setFixedSize(28, 28)
        self.toggle_btn.clicked.connect(self._toggle_expand)
        self.header.addStretch(1)
        self.header.addWidget(self.toggle_btn)
        lay.addLayout(self.header)
        # hide expand icon by default; appears on hover
        self.toggle_btn.hide()

        # Body frame to host content of specialized widgets
        self.body = QFrame(self)
        self.body.setLayout(QVBoxLayout())
        self.body.layout().setContentsMargins(0, 0, 0, 0)
        lay.addWidget(self.body)

        self.update_dark_mode(self.dark_mode)
        self.setAcceptDrops(True)

    # Show/hide expand icon on hover
    def enterEvent(self, event):
        try:
            # If expanded, ensure the close icon is visible
            container = self.parentWidget()
            while container is not None and not hasattr(container, "_expanded_widget"):
                container = container.parentWidget()
            if container is not None and getattr(container, "_expanded_widget", None) is self:
                self.toggle_btn.setText("×")
                self.toggle_btn.show()
            else:
                self.toggle_btn.setText("⛶")
                self.toggle_btn.show()
        except Exception:
            pass
        super().enterEvent(event)

    def leaveEvent(self, event):
        try:
            # Keep the close icon visible while expanded; otherwise hide on leave
            container = self.parentWidget()
            while container is not None and not hasattr(container, "_expanded_widget"):
                container = container.parentWidget()
            if container is not None and getattr(container, "_expanded_widget", None) is self:
                self.toggle_btn.setText("×")
                self.toggle_btn.show()
            else:
                self.toggle_btn.hide()
        except Exception:
            pass
        super().leaveEvent(event)

    def update_dark_mode(self, dark_mode: bool):
        
        from nano_style import get_content_style
        self.dark_mode = dark_mode
        
        # Désactiver temporairement les mises à jour pour éviter le clignotement
        self.setUpdatesEnabled(False)
        
        # Style spécifique pour les boutons
        fg = "#fff" if dark_mode else "#000"
        hover_bg = "rgba(255,255,255,0.08)" if dark_mode else "rgba(0,0,0,0.06)"
        btn_style = f"""
            QPushButton {{
                background: transparent;
                border: none;
                color: {fg};
                font-size: 14px;
                padding: 2px;
            }}
            QPushButton:hover {{
                background: {hover_bg};
                border-radius: 6px;
            }}
        """
        
        # Réinitialiser le style
        self.setStyleSheet("")
        
        # Appliquer le style du contenu + boutons
        content_style = get_content_style(dark_mode)
        combined_style = content_style + btn_style
        self.setStyleSheet(combined_style)

        # Appliquer le style spécifique au bouton
        self.toggle_btn.setStyleSheet(btn_style)
        
        
        # Style pour le body (transparent)
        self.body.setStyleSheet("QFrame { background: transparent; }")
        
        # Forcer la mise à jour du style
        self.style().unpolish(self)
        self.style().polish(self)
        
        # Réactiver les mises à jour et forcer le rafraîchissement
        self.setUpdatesEnabled(True)
        self.repaint()


    # Expand/Collapse behavior (simple hide/show of body)
    def _toggle_expand(self):
        # Full expand/collapse over the entire dashboard area (3x4)
        container = self.parentWidget()
        while container is not None and not hasattr(container, "expand_widget"):
            container = container.parentWidget()
        if container is None:
            # fallback to simple collapse of body if no container API
            self._is_collapsed = not self._is_collapsed
            self.body.setVisible(not self._is_collapsed)
            return

        if getattr(container, "_expanded_widget", None) is self:
            container.collapse_widget(self)
            # update icon to expand
            self.toggle_btn.setText("⛶")
        else:
            container.expand_widget(self)
            # update icon to close
            self.toggle_btn.setText("×")

    # Drag & drop to enable swapping between slots
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.position()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if not (event.buttons() & Qt.LeftButton):
            return
        start = getattr(self, "drag_start_position", None)
        if start is None:
            return
        if (event.position() - start).manhattanLength() < QApplication.startDragDistance():
            return

        drag = QDrag(self)
        mime = QMimeData()
        mime.setText(" ")  # sentinel used by container to identify swaps
        drag.setMimeData(mime)
        drag.exec_(Qt.MoveAction)

    def dragEnterEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().text() == " ":
            event.acceptProposedAction()

    def dropEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().text() == " ":
            src = event.source()
            # Find ancestor that knows how to swap
            p = self.parentWidget()
            while p is not None and not hasattr(p, "swap_widgets"):
                p = p.parentWidget()
            if p is not None:
                p.swap_widgets(src, self)
                event.acceptProposedAction()




class NanoDashboard(QWidget):
    def __init__(self, dark_mode: bool = True):
        super().__init__()
        self.dark_mode = dark_mode
        self.contents = {}

        # Scroll container (no border)
        self.scroll = QScrollArea(self)
        self.scroll.setWidgetResizable(True)
        self.scroll.setFrameShape(QFrame.NoFrame)
        self.scroll.setStyleSheet("QScrollArea { border: none; } QScrollArea > QWidget > QWidget { background: transparent; }")

        # Content widget and grid
        self.content = QWidget()
        self.grid = QGridLayout(self.content)
        self.grid.setContentsMargins(4, 4, 4, 4)
        self.grid.setSpacing(4)
        self.scroll.setWidget(self.content)

        # Main layout
        root = QVBoxLayout(self)
        root.setContentsMargins(0, 0, 0, 0)
        root.setSpacing(0)  # Supprime l'espace entre les widgets
        root.addWidget(self.scroll)

        # Create explicit 3x4 slots and mapping
        self._expanded_widget = None
        self._create_slots()
        self._configure_stretch()

        self.update_dark_mode(self.dark_mode)

        # Initialiser l'analyzer et la mise à jour automatique
        self.setup_analyzer_and_updates()



    def _create_slots(self):
        # Fill a 3x4 grid (12 slots) with ContentWidgetWithNanoContainer
        from nano_content_widget import ContentWidgetWithNanoContainer, CONTAINER_CONFIGS

        self.slot_order = []  # ordered list of widgets content1..content12
        self.slot_keys = {}   # widget -> "contentN"
        self.slot_map = {}    # "contentN" -> user-defined name (developer will set later)

        for i in range(12):
            r = i // 4
            c = i % 4

            # Récupérer la configuration pour ce container
            config = CONTAINER_CONFIGS[i] if i < len(CONTAINER_CONFIGS) else {
                "title": f"Container {i+1}",
                "type": "normal",
                "analysis": "generic",
                "status": "neutral"
            }

            # Créer le widget de contenu avec NanoContainer intégré
            w = ContentWidgetWithNanoContainer(self.dark_mode, parent=self, container_config=config)

            # S'assurer que l'ID est bien défini
            w.setObjectName('content-widget')

            # Ajouter le widget à la grille
            self.grid.addWidget(w, r, c)
            self.contents[w] = (r, c)

            # Mettre à jour le style immédiatement
            w.update_dark_mode(self.dark_mode)

            # Configurer les clés et la carte des emplacements
            key = f"content{i+1}"
            self.slot_order.append(w)
            self.slot_keys[w] = key
            self.slot_map[key] = config["title"]  # Utiliser le titre de la config

    def _configure_stretch(self):
        # Configuration plus stricte pour forcer l'adaptation
        for col in range(4):
            self.grid.setColumnStretch(col, 1)
            self.grid.setColumnMinimumWidth(col, 300)  # Largeur minimale
        for row in range(3):
            self.grid.setRowStretch(row, 1)
            self.grid.setRowMinimumHeight(row, 250)  # Hauteur minimale réduite

    def update_dark_mode(self, dark: bool):
        self.dark_mode = dark
        # Mettre à jour le style de chaque widget de contenu
        for w in self.contents.keys():
            if hasattr(w, "update_dark_mode"):
                # Forcer la mise à jour du style
                w.setStyleSheet("")
                w.update_dark_mode(dark)
                # Forcer la mise à jour de l'affichage
                w.update()
        # Forcer la mise à jour de l'interface
        self.update()

    # Backward-compatibility shim for callers expecting set_dark_mode()
    def set_dark_mode(self, dark: bool):
        self.update_dark_mode(dark)

    # Public API similar to parent: swap two slot widgets
    def swap_widgets(self, w1: QWidget, w2: QWidget):
        if w1 not in self.contents or w2 not in self.contents:
            return
        r1, c1 = self.contents[w1]
        r2, c2 = self.contents[w2]
        self.grid.addWidget(w1, r2, c2)
        self.grid.addWidget(w2, r1, c1)
        self.contents[w1], self.contents[w2] = (r2, c2), (r1, c1)
        # keep slot_keys order consistent by swapping keys
        if w1 in self.slot_keys and w2 in self.slot_keys:
            self.slot_keys[w1], self.slot_keys[w2] = self.slot_keys[w2], self.slot_keys[w1]
        # also swap in slot_order if present
        try:
            i1 = self.slot_order.index(w1)
            i2 = self.slot_order.index(w2)
            self.slot_order[i1], self.slot_order[i2] = self.slot_order[i2], self.slot_order[i1]
        except ValueError:
            pass

    # Expand/collapse over entire 3x4 grid
    def expand_widget(self, w: QWidget):
        if self._expanded_widget is not None and self._expanded_widget is not w:
            self.collapse_widget(self._expanded_widget)
        if w in self.contents:
            r, c = self.contents[w]
            w._original_pos = (r, c)
            # Hide all other widgets during expand
            self._hidden_on_expand = []
            for other in list(self.contents.keys()):
                if other is not w:
                    if other.isVisible():
                        self._hidden_on_expand.append(other)
                    other.setVisible(False)
            self.grid.addWidget(w, 0, 0, 3, 4)
            self.contents[w] = (0, 0)
            self._expanded_widget = w

    def collapse_widget(self, w: QWidget):
        if getattr(w, "_original_pos", None) is not None:
            r, c = w._original_pos
            self.grid.addWidget(w, r, c)
            self.contents[w] = (r, c)
            w._original_pos = None
        # Restore visibility of previously hidden widgets
        if hasattr(self, "_hidden_on_expand"):
            for other in self._hidden_on_expand:
                other.setVisible(True)
            self._hidden_on_expand = []
        if self._expanded_widget is w:
            self._expanded_widget = None

    # Helper to write to terminal
    def display_message_to_terminal(self, title, message, level="info"):
        # broadcast to all slots that support append_message (none named explicitly)
        for w in self.contents.keys():
            if hasattr(w, "append_message"):
                w.append_message(title, message, level)

    def setup_analyzer_and_updates(self):
        """Configure l'analyzer et la mise à jour automatique des containers"""
        try:
            from nano_analyses import MarketAnalyzer
            from nano_processor import DataProcessor
            from PySide6.QtCore import QTimer

            # Créer le data processor (optionnel)
            try:
                self.data_processor = DataProcessor("market_data.db")
                self.analyzer = MarketAnalyzer(self.data_processor)
                print("✅ Dashboard: Analyzer avec DataProcessor")
            except:
                self.analyzer = MarketAnalyzer()
                print("✅ Dashboard: Analyzer standalone")

            # Timer pour mise à jour automatique
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_all_nano_containers)
            self.update_timer.start(30000)  # 30 secondes

            # Première mise à jour après 2 secondes
            QTimer.singleShot(2000, self.update_all_nano_containers)

        except Exception as e:
            print(f"⚠️ Dashboard: Erreur setup analyzer: {e}")
            self.analyzer = None

    def update_all_nano_containers(self):
        """Met à jour tous les NanoContainers avec les données de l'analyzer"""
        if not hasattr(self, 'analyzer') or not self.analyzer:
            return

        print("🔄 Dashboard: Mise à jour des NanoContainers...")

        for widget in self.contents.keys():
            if hasattr(widget, 'update_container_data'):
                try:
                    widget.update_container_data(self.analyzer)
                except Exception as e:
                    print(f"Erreur mise à jour container: {e}")

    def get_nano_containers(self):
        """Retourne la liste de tous les NanoContainers"""
        containers = []
        for widget in self.contents.keys():
            if hasattr(widget, 'get_nano_container'):
                nano_container = widget.get_nano_container()
                if nano_container:
                    containers.append(nano_container)
        return containers

    def force_refresh_containers(self):
        """Force la mise à jour immédiate de tous les containers"""
        self.update_all_nano_containers()
